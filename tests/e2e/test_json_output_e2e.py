"""
End-to-end tests for JSON output functionality.
Tests the complete CLI workflow with --json flag as required by Story 4.3.
"""

import json
import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from unittest.mock import patch, MagicMock
from llama_tune.cli import app
from llama_tune.core.data_models import SystemProfile, ModelProfile, BenchmarkResult, GpuInfo


runner = CliRunner()


class TestJSONOutputE2E:
    """End-to-end tests for JSON output functionality."""

    @patch('llama_tune.cli.analyzer_get_system_profile')
    @patch('llama_tune.cli.analyzer_get_model_profile')
    @patch('llama_tune.cli.analyzer_run_feasibility_check')
    def test_json_output_non_interactive_mode(self, mock_feasibility, mock_get_model_profile, mock_get_system_profile):
        """Test JSON output in non-interactive mode."""
        # Mock system and model profiles
        mock_system_profile = SystemProfile(
            cpu_cores=8,
            total_ram_gb=16.0,
            gpus=[GpuInfo(model_name="RTX 4090", vram_gb=24.0)],
            numa_detected=False,
            blas_backend="cuBLAS"
        )
        
        mock_model_profile = ModelProfile(
            file_path="/test/model.gguf",
            architecture="llama",
            layer_count=32,
            quantization_type="Q4_0"
        )
        
        mock_get_system_profile.return_value = mock_system_profile
        mock_get_model_profile.return_value = mock_model_profile
        mock_feasibility.return_value = None  # No exception means feasible

        # Run CLI with --json flag
        result = runner.invoke(app, ["--model-path", "/test/model.gguf", "--json"])
        
        # AC 1: Tool's entire standard output is a single, valid JSON object
        assert result.exit_code == 0
        
        # Should be able to parse as JSON
        parsed_json = json.loads(result.stdout)
        assert isinstance(parsed_json, dict)
        
        # AC 2: JSON object contains all required information
        assert "system_profile" in parsed_json
        assert "model_profile" in parsed_json
        assert "best_benchmark_result" in parsed_json
        assert "generated_command" in parsed_json
        
        # AC 4: No other text should be present (only JSON)
        # The entire stdout should be valid JSON
        lines = result.stdout.strip().split('\n')
        full_json = '\n'.join(lines)
        json.loads(full_json)  # Should not raise an exception

    @patch('llama_tune.benchmarker.benchmarking_engine.BenchmarkingEngine')
    @patch('llama_tune.analyzer.analyzer.get_system_profile')
    @patch('llama_tune.analyzer.analyzer.get_model_profile')
    @patch('llama_tune.analyzer.analyzer.run_feasibility_check')
    def test_json_output_benchmark_mode(self, mock_feasibility, mock_get_model_profile, 
                                       mock_get_system_profile, mock_benchmarking_engine):
        """Test JSON output in benchmark mode."""
        # Mock system and model profiles
        mock_system_profile = SystemProfile(
            cpu_cores=16,
            total_ram_gb=32.0,
            gpus=[GpuInfo(model_name="RTX 4090", vram_gb=24.0)],
            numa_detected=True,
            blas_backend="cuBLAS"
        )
        
        mock_model_profile = ModelProfile(
            file_path="/test/benchmark_model.gguf",
            architecture="llama",
            layer_count=40,
            quantization_type="Q4_K_M"
        )
        
        mock_benchmark_result = BenchmarkResult(
            n_gpu_layers=20,
            prompt_speed_tps=15.0,
            generation_speed_tps=30.0,
            batch_size=512,
            parallel_level=4,
            prompt_speed_tps_mean=15.2,
            prompt_speed_tps_std=0.8,
            generation_speed_tps_mean=30.5,
            generation_speed_tps_std=1.2
        )
        
        # Mock the benchmarking engine
        mock_engine_instance = MagicMock()
        mock_engine_instance.run_benchmark.return_value = (
            # OptimalConfiguration will be created by the CLI
            None,  # We'll let the CLI create this
            [mock_benchmark_result]  # all_benchmark_results
        )
        mock_benchmarking_engine.return_value = mock_engine_instance
        
        mock_get_system_profile.return_value = mock_system_profile
        mock_get_model_profile.return_value = mock_model_profile
        mock_feasibility.return_value = None  # No exception means feasible
        
        # We need to mock the actual return value more carefully
        from llama_tune.core.data_models import OptimalConfiguration
        optimal_config = OptimalConfiguration(
            system_profile=mock_system_profile,
            model_profile=mock_model_profile,
            best_benchmark_result=mock_benchmark_result,
            generated_command="",
            notes=["Benchmark completed"],
            ctx_size=4096,
            use_case="default"
        )
        
        mock_engine_instance.run_benchmark.return_value = (optimal_config, [mock_benchmark_result])
        
        # Run CLI with --benchmark and --json flags
        result = runner.invoke(app, [
            "--benchmark", 
            "--model-path", "/test/benchmark_model.gguf",
            "--ctx-size", "4096",
            "--json"
        ])
        
        # Should succeed
        assert result.exit_code == 0
        
        # Should be valid JSON
        parsed_json = json.loads(result.stdout)
        
        # Should contain benchmark results
        assert parsed_json["best_benchmark_result"]["n_gpu_layers"] == 20
        assert parsed_json["best_benchmark_result"]["prompt_speed_tps_mean"] == 15.2
        assert parsed_json["best_benchmark_result"]["generation_speed_tps_mean"] == 30.5
        
        # Should contain generated command with benchmark-specific settings
        generated_command = parsed_json["generated_command"]
        assert "llama-server" in generated_command
        assert "--n-gpu-layers 20" in generated_command
        assert "--ctx-size 4096" in generated_command
        assert "--numa distribute" in generated_command  # NUMA detected
        assert "--use-cublas" in generated_command  # cuBLAS backend

    @patch('llama_tune.wizard.interactive_wizard.InteractiveWizard')
    @patch('llama_tune.analyzer.analyzer.get_system_profile')
    @patch('llama_tune.analyzer.analyzer.get_model_profile')
    def test_json_output_interactive_mode(self, mock_get_model_profile, mock_get_system_profile, mock_wizard_class):
        """Test JSON output in interactive mode."""
        # Mock system and model profiles
        mock_system_profile = SystemProfile(
            cpu_cores=4,
            total_ram_gb=8.0,
            gpus=[],
            numa_detected=False,
            blas_backend="None"
        )
        
        mock_model_profile = ModelProfile(
            file_path="/test/interactive_model.gguf",
            architecture="llama",
            layer_count=24,
            quantization_type="Q5_K_M"
        )
        
        mock_benchmark_result = BenchmarkResult(
            n_gpu_layers=0,  # CPU-only
            prompt_speed_tps=8.0,
            generation_speed_tps=12.0,
            batch_size=None,
            parallel_level=None
        )
        
        # Mock the wizard
        mock_wizard_instance = MagicMock()
        from llama_tune.core.data_models import OptimalConfiguration
        optimal_config = OptimalConfiguration(
            system_profile=mock_system_profile,
            model_profile=mock_model_profile,
            best_benchmark_result=mock_benchmark_result,
            generated_command="",
            notes=["Interactive configuration"],
            ctx_size=2048,
            sampling_parameters={"temperature": 0.8, "top_p": 0.95}
        )
        
        mock_wizard_instance.get_optimal_configuration.return_value = optimal_config
        mock_wizard_class.return_value = mock_wizard_instance
        
        mock_get_system_profile.return_value = mock_system_profile
        mock_get_model_profile.return_value = mock_model_profile
        
        # Run CLI with --interactive and --json flags
        result = runner.invoke(app, ["--interactive", "--json"], input="\n")  # Provide empty input to avoid hanging
        
        # Should succeed
        assert result.exit_code == 0
        
        # Should be valid JSON
        parsed_json = json.loads(result.stdout)
        
        # Should contain interactive configuration
        assert parsed_json["ctx_size"] == 2048
        assert parsed_json["sampling_parameters"]["temperature"] == 0.8
        assert parsed_json["sampling_parameters"]["top_p"] == 0.95
        
        # Should contain generated command with sampling parameters
        generated_command = parsed_json["generated_command"]
        assert "llama-server" in generated_command
        assert "--temperature 0.8" in generated_command
        assert "--top_p 0.95" in generated_command

    def test_json_output_schema_stability(self):
        """Test that JSON output schema is stable and well-defined (AC 3)."""
        with patch('llama_tune.analyzer.analyzer.get_system_profile') as mock_get_system_profile, \
             patch('llama_tune.analyzer.analyzer.get_model_profile') as mock_get_model_profile, \
             patch('llama_tune.analyzer.analyzer.run_feasibility_check') as mock_feasibility:
            
            # Mock minimal profiles
            mock_system_profile = SystemProfile(
                cpu_cores=2, total_ram_gb=4.0, gpus=[], numa_detected=False, blas_backend="None"
            )
            mock_model_profile = ModelProfile(
                file_path="/test/schema_model.gguf", architecture="llama", 
                layer_count=16, quantization_type="Q4_0"
            )
            
            mock_get_system_profile.return_value = mock_system_profile
            mock_get_model_profile.return_value = mock_model_profile
            mock_feasibility.return_value = None  # No exception means feasible

            # Run CLI with --json
            result = runner.invoke(app, ["--model-path", "/test/schema_model.gguf", "--json"])
            
            assert result.exit_code == 0
            parsed_json = json.loads(result.stdout)
            
            # AC 3: JSON schema is stable and well-defined
            # Check that all expected top-level keys are present
            expected_keys = {
                "system_profile", "model_profile", "best_benchmark_result", 
                "generated_command", "notes", "ctx_size", "sampling_parameters",
                "use_case", "max_vram_gb"
            }
            
            actual_keys = set(parsed_json.keys())
            assert expected_keys.issubset(actual_keys), f"Missing keys: {expected_keys - actual_keys}"
            
            # Check nested structure stability
            assert isinstance(parsed_json["system_profile"], dict)
            assert isinstance(parsed_json["model_profile"], dict)
            assert isinstance(parsed_json["best_benchmark_result"], dict)
            assert isinstance(parsed_json["generated_command"], str)
            assert isinstance(parsed_json["notes"], list)

    def test_json_output_no_extra_text_to_stdout(self):
        """Test that no other text is printed to stdout when --json is active (AC 4)."""
        with patch('llama_tune.analyzer.analyzer.get_system_profile') as mock_get_system_profile, \
             patch('llama_tune.analyzer.analyzer.get_model_profile') as mock_get_model_profile, \
             patch('llama_tune.analyzer.analyzer.run_feasibility_check') as mock_feasibility:
            
            mock_system_profile = SystemProfile(
                cpu_cores=4, total_ram_gb=8.0, gpus=[], numa_detected=False, blas_backend="None"
            )
            mock_model_profile = ModelProfile(
                file_path="/test/clean_model.gguf", architecture="llama", 
                layer_count=20, quantization_type="Q4_0"
            )
            
            mock_get_system_profile.return_value = mock_system_profile
            mock_get_model_profile.return_value = mock_model_profile
            mock_feasibility.return_value = None  # No exception means feasible

            # Run CLI with --json
            result = runner.invoke(app, ["--model-path", "/test/clean_model.gguf", "--json"])
            
            assert result.exit_code == 0
            
            # AC 4: The tool should not print any other text to standard output when --json is active
            # The entire stdout should be parseable as a single JSON object
            stdout_content = result.stdout.strip()
            
            # Should start with { and end with }
            assert stdout_content.startswith('{')
            assert stdout_content.endswith('}')
            
            # Should be parseable as a single JSON object
            parsed_json = json.loads(stdout_content)
            assert isinstance(parsed_json, dict)
            
            # Should not contain any non-JSON text
            # If there were extra text, JSON parsing would fail or we'd have multiple lines of non-JSON
            lines = stdout_content.split('\n')
            # All lines together should form valid JSON
            full_content = '\n'.join(lines)
            json.loads(full_content)  # Should not raise an exception

    def test_json_output_error_handling(self):
        """Test JSON output when errors occur."""
        # Test with missing model file
        result = runner.invoke(app, ["--model-path", "/nonexistent/model.gguf", "--json"])
        
        # Should exit with error code
        assert result.exit_code != 0
        
        # In error cases, we might not have JSON output, but let's check if we do
        if result.stdout.strip():
            try:
                # If there's output, it should be valid JSON (error JSON)
                parsed_json = json.loads(result.stdout)
                # Could contain error information
                assert isinstance(parsed_json, dict)
            except json.JSONDecodeError:
                # If it's not JSON, that's also acceptable for error cases
                # as long as the exit code indicates failure
                pass
