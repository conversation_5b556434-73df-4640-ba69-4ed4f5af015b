import typer
import logging
import sys
import importlib
from typing_extensions import Annotated
from typing import Optional
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.console import Console
from llama_tune.analyzer.analyzer import get_system_profile as analyzer_get_system_profile, get_model_profile as analyzer_get_model_profile, run_feasibility_check as analyzer_run_feasibility_check, get_model_metadata, FeasibilityError
from llama_tune.core.data_models import OptimalConfiguration, BenchmarkResult, ModelProfile
from llama_tune.reporting.output_generator import generate_output
from llama_tune.benchmarker.benchmarking_engine import BenchmarkingEngine, ProgressCallback

app = typer.Typer()
logger = logging.getLogger(__name__)

@app.command()
def tune(
    model_path: Annotated[Optional[str], typer.Option("--model-path", "-m", help="Path to the GGUF model file.")] = None,
    verbose: Annotated[bool, typer.Option(help="Enable verbose output.")] = False,
    json_output: Annotated[bool, typer.Option(help="Output results as JSON.")] = False,
    interactive: Annotated[bool, typer.Option("--interactive", help="Launch an interactive setup wizard.")] = False,
    benchmark: Annotated[bool, typer.Option("--benchmark", help="Initiate automated benchmarking.")] = False,
    ctx_size: Annotated[Optional[int], typer.Option("--ctx-size", help="Target context size for benchmarking.")] = None,
    max_vram_gb: Annotated[Optional[float], typer.Option("--max-vram-gb", help="Maximum GPU VRAM to utilize in GB.")] = None,
    llama_server_url: Annotated[Optional[str], typer.Option("--llama-server-url", help="URL of the llama-server to use for benchmarking.")] = None,
    num_runs: Annotated[int, typer.Option("--num-runs", "-n", help="Number of times to run each benchmark configuration for statistical analysis.")] = 3,
    use_case: Annotated[str, typer.Option("--use-case", help="Use case for benchmarking: 'default' or 'multi-user-server'.")] = "default",
    plot: Annotated[bool, typer.Option("--plot", help="Generate an interactive HTML plot of the Pareto front.")] = False,
):
    """
    Analyzes system hardware and GGUF model to recommend optimal llama.cpp settings.
    """
    logging.basicConfig(level=logging.INFO if verbose else logging.WARNING)

    # Validate required arguments for benchmark mode
    if benchmark:
        if not model_path:
            typer.echo("Error: --model-path is required when using --benchmark.")
            print("DEBUG: Exiting due to missing model_path")
            sys.exit(1)
        if not ctx_size:
            typer.echo("Error: --ctx-size is required when using --benchmark.")
            sys.exit(1)

    # 1. Detect System Profile
    system_profile = analyzer_get_system_profile()
    logger.info(f"Detected System Profile: {system_profile}")

    # 2. Get Model Profile
    model_profile = None
    if model_path:
        try:
            print(f"DEBUG: Calling get_model_profile with {model_path}")
            model_profile = analyzer_get_model_profile(model_path)
            logger.info(f"Detected Model Profile: {model_profile}")
        except FeasibilityError as e:
            typer.echo(f"Error: {e}")
            sys.exit(1)

    # 3. Perform Pre-flight Feasibility Check
    if model_profile and system_profile:
        try:
            analyzer_run_feasibility_check(model_profile, system_profile)
        except FeasibilityError as e:
            typer.echo(f"Error: {e}")
            sys.exit(1)

    if interactive:
        typer.echo("Welcome to the Llama-Tune Interactive Setup Wizard!")
        typer.echo("This wizard will guide you through configuring optimal settings for your GGUF model.")
        from llama_tune.wizard.interactive_wizard import InteractiveWizard
        wizard = InteractiveWizard()
        try:
            wizard.start()
        except (KeyboardInterrupt, EOFError):
            typer.echo("Wizard interrupted.")
            sys.exit(1)
        
        optimal_config = wizard.get_optimal_configuration()
        if optimal_config:
            output_mode = "json" if json_output else ("verbose" if verbose else "default")
            final_output = generate_output(optimal_config, output_mode)

            if output_mode == "default":
                # Default mode: only output the command string to stdout
                typer.echo(final_output)
            else:
                # Verbose/JSON modes: include additional information
                typer.echo("\n" + final_output)
                typer.echo("Configuration complete!")
        else:
            typer.echo("Wizard did not complete successfully. No command generated.")
        sys.exit()


    if benchmark:
        if model_path is None:
            typer.echo("Error: --model-path is required when using --benchmark.")
            sys.exit(1)
        if ctx_size is None:
            typer.echo("Error: --ctx-size is required when using --benchmark.")
            sys.exit(1)
        
        # Task 3: Call BenchmarkingEngine.run_benchmark()
        benchmarking_engine = BenchmarkingEngine(llama_server_url=llama_server_url)
        
        console = Console()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            TimeRemainingColumn(),
            
            console=console,
            transient=True,
        ) as progress:
            overall_task = progress.add_task("[green]Overall Benchmark Progress", total=100)

            def update_progress_callback(phase_name: str, step_description: str, current_step: int, total_steps: int, speed: Optional[float] = None, run_idx: Optional[int] = None, num_runs: Optional[int] = None):
                progress_percentage = (current_step / total_steps) * 100 if total_steps > 0 else 0
                run_info = f" (Run {run_idx}/{num_runs})" if run_idx is not None and num_runs is not None else ""
                speed_info = f" - {speed:.2f} t/s" if speed is not None else ""
                progress.update(overall_task, description=f"[green]{phase_name}: [cyan]{step_description}{run_info}{speed_info}", completed=progress_percentage)
                
            optimal_config, all_benchmark_results = benchmarking_engine.run_benchmark(model_path, ctx_size, use_case, num_runs, update_progress_callback, max_vram_gb)

        output_mode = "json" if json_output else ("verbose" if verbose else "default")
        plot_file_path = "pareto_plot.html" if plot else None
        final_output = generate_output(optimal_config, output_mode, all_benchmark_results, plot_file_path)

        if output_mode == "default":
            # Default mode: only output the command string to stdout
            typer.echo(final_output)
        else:
            # Verbose/JSON modes: include additional information
            typer.echo("\n" + final_output)
            typer.echo("Benchmarking complete!")
        sys.exit()

    if not interactive and not benchmark:
        # The logic for generating optimal_config for non-interactive mode
        # will remain here for now, but will be refactored in future stories
        # when benchmarking is implemented.
        dummy_benchmark_result = BenchmarkResult(
            n_gpu_layers=0,
            prompt_speed_tps=0.0,
            generation_speed_tps=0.0,
            batch_size=None,
            parallel_level=None
        )

        # For non-interactive mode, we create the configuration with default values.
        # The OutputGenerator will build the complete command from this data.
        notes = []
        if system_profile.cpu_cores == 0:
            notes.append("Could not detect physical CPU cores. The --threads argument was omitted from the command.")

        optimal_config = OptimalConfiguration(
            system_profile=system_profile,
            model_profile=model_profile,
            best_benchmark_result=dummy_benchmark_result,
            generated_command="",  # Empty - OutputGenerator will build the complete command
            notes=notes,
            ctx_size=2048,  # Default context size for non-interactive
            sampling_parameters={}  # No sampling parameters in non-interactive mode
        )
        
        # For non-interactive mode, we don't need a progress bar, just print the final output

        output_mode = "json" if json_output else ("verbose" if verbose else "default")
        final_output = generate_output(optimal_config, output_mode)

        # Default mode outputs only the command, verbose/JSON modes can include extra info
        typer.echo(final_output)

if __name__ == "__main__":
    import asyncio
    asyncio.run(app())